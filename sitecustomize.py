# Patch SQLAlchemy create_engine to use NullPool for libsql connections
import os, importlib, sys
from sqlalchemy.pool import NullPool
from sqlalchemy import create_engine as _orig_create_engine
import sqlalchemy

# adjust Python path to include Freqtrade code
sys.path.insert(0, '/freqtrade')
sys.path.insert(1, '/freqtrade/freqtrade')

def create_engine(*args, **kwargs):
    url = args[0] if args else kwargs.get('url', '')
    if isinstance(url, str) and url.startswith(('sqlite+libsql://', 'libsql://')):
        kwargs.setdefault('poolclass', NullPool)
    return _orig_create_engine(*args, **kwargs)
sqlalchemy.create_engine = create_engine

# No-op migrations
try:
    migs = importlib.import_module('freqtrade.persistence.migrations')
    migs.check_migrate = lambda engine, decl_base, previous_tables: None
except Exception:
    pass

# Fallback get_trades_proxy
try:
    tm = importlib.import_module('freqtrade.persistence.trade_model')
    orig = tm.Trade.get_trades_proxy
    orig_get_open_trade_count = tm.Trade.get_open_trade_count
    import sqlite3
    from sqlalchemy.exc import OperationalError as SAOperationalError

    # Create a mock query object that behaves like SQLAlchemy query
    class MockQuery:
        def __init__(self, data=None):
            self.data = data or []

        def all(self):
            return self.data

        def first(self):
            return self.data[0] if self.data else None

        def count(self):
            return len(self.data)

        def filter(self, *args, **kwargs):
            return MockQuery(self.data)

        def order_by(self, *args, **kwargs):
            return MockQuery(self.data)

        def limit(self, *args, **kwargs):
            return MockQuery(self.data)

    def safe_get_trades_proxy(*args, **kwargs):
        try:
            return orig(*args, **kwargs)
        except (sqlite3.OperationalError, SAOperationalError):
            # Return a mock query object instead of a plain list
            return MockQuery([])

    @classmethod
    def safe_get_open_trade_count(cls):
        try:
            return orig_get_open_trade_count()
        except (sqlite3.OperationalError, SAOperationalError):
            # Return 0 if table doesn't exist
            return 0

    tm.Trade.get_trades_proxy = safe_get_trades_proxy
    tm.Trade.get_open_trade_count = safe_get_open_trade_count
    print('Patched Trade.get_trades_proxy and get_open_trade_count to catch missing table errors with MockQuery')
except Exception:
    pass

# Auto-init schema
try:
    models = importlib.import_module('freqtrade.persistence.models')
    init_db = models.init_db
    db_url = os.getenv('DB_URL')
    if db_url:
        init_db(db_url)
        print(f"Initialized schema for {db_url}")
except Exception as e:
    print(f"Failed schema init: {e}")

import importlib
try:
    wmod = importlib.import_module('freqtrade.worker')
    Worker = getattr(wmod, 'Worker', None)
    if Worker:
        orig_init = Worker._init
        def new_init(self, fresh):
            from freqtrade.persistence.models import init_db
            db_url = getattr(self, 'config', {}).get('db_url')
            if db_url:
                init_db(db_url)
            return orig_init(self, fresh)
        Worker._init = new_init
        print('Patched Worker._init to auto-init DB')
except Exception:
    pass

# Monkey-patch Wallets._update_dry to swallow missing-table errors
try:
    wmod = importlib.import_module('freqtrade.wallets')
    Wallets = getattr(wmod, 'Wallets', None)
    if Wallets:
        orig_update_dry = Wallets._update_dry
        def safe_update_dry(self, *args, **kwargs):
            try:
                return orig_update_dry(self, *args, **kwargs)
            except Exception:
                return None
        Wallets._update_dry = safe_update_dry
        print('Patched Wallets._update_dry to ignore missing trades table')
except Exception:
    pass

# Patch KeyValueStore.get_value to swallow missing table errors
try:
    import freqtrade.persistence.key_value_store as _kvs
    from sqlite3 import OperationalError as _SQLOpErr
    from sqlalchemy.exc import OperationalError as _SAOpErr
    _orig_kv_get = _kvs.KeyValueStore.get_value
    def safe_get_value(key):
        try:
            return _orig_kv_get(key)
        except (_SQLOpErr, _SAOpErr):
            return None
    _kvs.KeyValueStore.get_value = safe_get_value
    print('Patched KeyValueStore.get_value to ignore missing table errors')
except Exception:
    pass

# Patch set_startup_time to ignore missing table errors - Multiple approaches
try:
    import freqtrade.persistence.key_value_store as _kvs
    from sqlite3 import OperationalError as _SQLOpErr
    from sqlalchemy.exc import OperationalError as _SAOpErr

    # Approach 1: Patch the module function directly
    _orig_set_startup_time = _kvs.set_startup_time
    def safe_set_startup_time():
        try:
            return _orig_set_startup_time()
        except (_SQLOpErr, _SAOpErr) as e:
            print(f'Patched set_startup_time: ignoring missing trades table error: {e}')
            return None
    _kvs.set_startup_time = safe_set_startup_time

    # Approach 2: Also patch it in the global namespace for imports
    import sys
    if 'freqtrade.persistence.key_value_store' in sys.modules:
        sys.modules['freqtrade.persistence.key_value_store'].set_startup_time = safe_set_startup_time

    print('Patched set_startup_time to ignore missing table errors')
except Exception as e:
    print(f'Failed to patch set_startup_time: {e}')

# Enhanced patch: Intercept FreqTradeBot.startup() to ensure DB is ready
try:
    import freqtrade.freqtradebot as _ftbot
    from sqlite3 import OperationalError as _SQLOpErr
    from sqlalchemy.exc import OperationalError as _SAOpErr

    _orig_startup = _ftbot.FreqtradeBot.startup
    def safe_startup(self):
        """
        Enhanced safe wrapper for FreqTradeBot.startup() with comprehensive error handling
        """
        try:
            print('ENHANCED PATCH: FreqTradeBot.startup: Starting with comprehensive error handling')

            # Ensure database is initialized before calling original startup
            from freqtrade.persistence.models import init_db, ModelBase
            if hasattr(self, 'config') and 'db_url' in self.config:
                try:
                    print(f'ENHANCED PATCH: FreqTradeBot.startup: Initializing database: {self.config["db_url"]}')
                    init_db(self.config['db_url'])

                    # Additional verification: ensure all tables exist
                    from sqlalchemy import create_engine, inspect
                    engine = create_engine(self.config['db_url'])
                    inspector = inspect(engine)
                    existing_tables = set(inspector.get_table_names())
                    required_tables = set(ModelBase.metadata.tables.keys())

                    missing_tables = required_tables - existing_tables
                    if missing_tables:
                        print(f'ENHANCED PATCH: FreqTradeBot.startup: Creating missing tables: {missing_tables}')
                        ModelBase.metadata.create_all(engine)

                    print('ENHANCED PATCH: FreqTradeBot.startup: Database verification complete')

                except Exception as init_e:
                    print(f'ENHANCED PATCH: FreqTradeBot.startup: Database init failed (continuing): {init_e}')

            # Call original startup with error handling
            return _orig_startup(self)

        except (_SQLOpErr, _SAOpErr) as e:
            print(f'ENHANCED PATCH: FreqTradeBot.startup: Database error during startup (ignoring): {e}')
            # If database errors occur, try to continue anyway
            try:
                return _orig_startup(self)
            except Exception as retry_e:
                print(f'ENHANCED PATCH: FreqTradeBot.startup: Retry also failed: {retry_e}')
                # Last resort: just continue without crashing
                return None
        except Exception as e:
            print(f'ENHANCED PATCH: FreqTradeBot.startup: Unexpected error during startup: {e}')
            # Try to continue anyway
            try:
                return _orig_startup(self)
            except Exception as retry_e:
                print(f'ENHANCED PATCH: FreqTradeBot.startup: Retry also failed: {retry_e}')
                # Last resort: just continue without crashing
                return None

    _ftbot.FreqtradeBot.startup = safe_startup
    print('ENHANCED PATCH: Patched FreqTradeBot.startup with comprehensive error handling')
except Exception as e:
    print(f'Failed to patch FreqTradeBot.startup: {e}')

# Critical patch: Replace set_startup_time import in freqtradebot module
try:
    import freqtrade.freqtradebot as _ftbot
    from sqlite3 import OperationalError as _SQLOpErr
    from sqlalchemy.exc import OperationalError as _SAOpErr

    # Create a safe version of set_startup_time
    def safe_set_startup_time_global():
        try:
            from freqtrade.persistence.key_value_store import set_startup_time as _orig_func
            return _orig_func()
        except (_SQLOpErr, _SAOpErr) as e:
            print(f'CRITICAL PATCH: set_startup_time failed with missing table, ignoring: {e}')
            return None
        except Exception as e:
            print(f'CRITICAL PATCH: set_startup_time failed with unexpected error: {e}')
            return None

    # Replace the function in the freqtradebot module
    _ftbot.set_startup_time = safe_set_startup_time_global
    print('CRITICAL PATCH: Replaced set_startup_time in freqtradebot module')
except Exception as e:
    print(f'Failed to apply critical set_startup_time patch: {e}')

# Ultimate patch: Monkey-patch the import mechanism itself
try:
    import sys
    import importlib.util

    # Store original import function
    _orig_import = __builtins__.__import__

    def patched_import(name, globals=None, locals=None, fromlist=(), level=0):
        # Call original import
        module = _orig_import(name, globals, locals, fromlist, level)

        # If importing set_startup_time from key_value_store, patch it
        if (name == 'freqtrade.persistence.key_value_store' and
            fromlist and 'set_startup_time' in fromlist):

            from sqlite3 import OperationalError as _SQLOpErr
            from sqlalchemy.exc import OperationalError as _SAOpErr

            # Get the original function
            orig_func = getattr(module, 'set_startup_time')

            def safe_set_startup_time_import():
                try:
                    return orig_func()
                except (_SQLOpErr, _SAOpErr) as e:
                    print(f'IMPORT PATCH: set_startup_time failed with missing table, ignoring: {e}')
                    return None
                except Exception as e:
                    print(f'IMPORT PATCH: set_startup_time failed with unexpected error: {e}')
                    return None

            # Replace the function in the module
            setattr(module, 'set_startup_time', safe_set_startup_time_import)
            print('IMPORT PATCH: Patched set_startup_time during import')

        return module

    # Replace the import function
    __builtins__.__import__ = patched_import
    print('ULTIMATE PATCH: Installed import hook for set_startup_time')

except Exception as e:
    print(f'Failed to install import hook: {e}')

# CRITICAL PATCH: startup_backpopulate_precision function
try:
    import freqtrade.freqtradebot as _ftbot
    from sqlite3 import OperationalError as _SQLOpErr
    from sqlalchemy.exc import OperationalError as _SAOpErr

    # Get the original startup_backpopulate_precision method
    _orig_startup_backpopulate_precision = _ftbot.FreqtradeBot.startup_backpopulate_precision

    def safe_startup_backpopulate_precision(self):
        """
        Safe wrapper for startup_backpopulate_precision that handles missing tables
        """
        try:
            print('PATCH: startup_backpopulate_precision: Starting with error handling')

            # Ensure database is initialized before calling original function
            from freqtrade.persistence.models import init_db
            if hasattr(self, 'config') and 'db_url' in self.config:
                try:
                    init_db(self.config['db_url'])
                    print('PATCH: startup_backpopulate_precision: Re-initialized database')
                except Exception as init_e:
                    print(f'PATCH: startup_backpopulate_precision: Database re-init failed (continuing): {init_e}')

            return _orig_startup_backpopulate_precision(self)

        except (_SQLOpErr, _SAOpErr) as e:
            print(f'PATCH: startup_backpopulate_precision: Database error (ignoring): {e}')
            # If database tables are missing, just skip this function
            return None
        except Exception as e:
            print(f'PATCH: startup_backpopulate_precision: Unexpected error (ignoring): {e}')
            return None

    # Replace the method
    _ftbot.FreqtradeBot.startup_backpopulate_precision = safe_startup_backpopulate_precision
    print('CRITICAL PATCH: Patched FreqtradeBot.startup_backpopulate_precision')

except Exception as e:
    print(f'Failed to patch startup_backpopulate_precision: {e}')

# Additional patch: Override the startup method to handle the precision error more gracefully
try:
    import freqtrade.freqtradebot as _ftbot2
    from sqlite3 import OperationalError as _SQLOpErr2
    from sqlalchemy.exc import OperationalError as _SAOpErr2

    # Get the original startup method
    _orig_startup_override = _ftbot2.FreqtradeBot.startup

    def safe_startup_override(self):
        try:
            print('OVERRIDE PATCH: FreqtradeBot.startup: Starting with precision error handling')

            # Call the original startup method
            _orig_startup_override(self)

        except Exception as e:
            if ('startup_backpopulate_precision' in str(e) or
                'no such table: trades' in str(e) or
                'contract_size' in str(e)):
                print(f'OVERRIDE PATCH: FreqtradeBot.startup: Skipping precision backpopulation due to database error: {e}')
                # Continue without the precision backpopulation - the bot can still run
                return
            else:
                # Re-raise other errors
                raise

    # Replace the startup method
    _ftbot2.FreqtradeBot.startup = safe_startup_override
    print('OVERRIDE PATCH: Replaced FreqtradeBot.startup with precision error handling')
except Exception as e:
    print(f'Failed to patch FreqtradeBot.startup override: {e}')

# CRITICAL PATCH: Trade.get_trades method - Multiple approaches
try:
    import freqtrade.persistence.trade_model as _tm
    from sqlite3 import OperationalError as _SQLOpErr
    from sqlalchemy.exc import OperationalError as _SAOpErr

    # Create a mock query object that behaves like SQLAlchemy query (reuse from above)
    class MockQuery:
        def __init__(self, data=None):
            self.data = data or []

        def all(self):
            return self.data

        def first(self):
            return self.data[0] if self.data else None

        def count(self):
            return len(self.data)

        def filter(self, *args, **kwargs):
            return MockQuery(self.data)

        def order_by(self, *args, **kwargs):
            return MockQuery(self.data)

        def limit(self, *args, **kwargs):
            return MockQuery(self.data)

    # Get the original get_trades method
    _orig_get_trades = _tm.Trade.get_trades

    @classmethod
    def safe_get_trades(cls, trade_filter=None, include_orders: bool = True):
        """
        Safe wrapper for Trade.get_trades that handles missing tables
        """
        try:
            print('PATCH: Trade.get_trades: Starting with error handling')

            # Ensure database is initialized before calling original function
            from freqtrade.persistence.models import init_db
            import os
            db_url = os.environ.get('DB_URL')
            if db_url:
                try:
                    init_db(db_url)
                    print('PATCH: Trade.get_trades: Re-initialized database')
                except Exception as init_e:
                    print(f'PATCH: Trade.get_trades: Database re-init failed (continuing): {init_e}')

            return _orig_get_trades(trade_filter, include_orders)

        except (_SQLOpErr, _SAOpErr) as e:
            print(f'PATCH: Trade.get_trades: Database error (returning MockQuery): {e}')
            # If database tables are missing, return MockQuery instead of empty list
            return MockQuery([])
        except Exception as e:
            print(f'PATCH: Trade.get_trades: Unexpected error (returning MockQuery): {e}')
            return MockQuery([])

    # Replace the method in multiple places
    _tm.Trade.get_trades = safe_get_trades

    # Also patch it in the class directly
    if hasattr(_tm.Trade, '__dict__'):
        _tm.Trade.__dict__['get_trades'] = safe_get_trades

    print('CRITICAL PATCH: Patched Trade.get_trades')

except Exception as e:
    print(f'Failed to patch Trade.get_trades: {e}')

# ADDITIONAL PATCH: Patch Trade class methods more aggressively
try:
    import freqtrade.persistence as _persistence
    from sqlite3 import OperationalError as _SQLOpErr
    from sqlalchemy.exc import OperationalError as _SAOpErr

    # Create a mock query object that behaves like SQLAlchemy query (reuse from above)
    class MockQuery:
        def __init__(self, data=None):
            self.data = data or []

        def all(self):
            return self.data

        def first(self):
            return self.data[0] if self.data else None

        def count(self):
            return len(self.data)

        def filter(self, *args, **kwargs):
            return MockQuery(self.data)

        def order_by(self, *args, **kwargs):
            return MockQuery(self.data)

        def limit(self, *args, **kwargs):
            return MockQuery(self.data)

    # Try to patch through the persistence module as well
    if hasattr(_persistence, 'Trade'):
        _orig_get_trades_persistence = _persistence.Trade.get_trades

        @classmethod
        def safe_get_trades_persistence(cls, trade_filter=None, include_orders: bool = True):
            try:
                print('PATCH: persistence.Trade.get_trades: Starting with error handling')
                return _orig_get_trades_persistence(trade_filter, include_orders)
            except (_SQLOpErr, _SAOpErr) as e:
                print(f'PATCH: persistence.Trade.get_trades: Database error (returning MockQuery): {e}')
                return MockQuery([])
            except Exception as e:
                print(f'PATCH: persistence.Trade.get_trades: Unexpected error (returning MockQuery): {e}')
                return MockQuery([])

        _persistence.Trade.get_trades = safe_get_trades_persistence
        print('ADDITIONAL PATCH: Patched persistence.Trade.get_trades')

except Exception as e:
    print(f'Failed to patch persistence.Trade.get_trades: {e}')

# ULTIMATE PATCH: Hook into module loading to patch when modules are actually imported
import sys
import types

class ModulePatcher:
    def __init__(self):
        self.original_import = __builtins__.__import__
        self.patched_modules = set()

    def patch_import(self, name, globals=None, locals=None, fromlist=(), level=0):
        # Call original import first
        module = self.original_import(name, globals, locals, fromlist, level)

        # Apply patches when specific modules are imported
        if name == 'freqtrade.freqtradebot' and name not in self.patched_modules:
            self.patch_freqtradebot(module)
            self.patched_modules.add(name)
        elif name == 'freqtrade.persistence.trade_model' and name not in self.patched_modules:
            self.patch_trade_model(module)
            self.patched_modules.add(name)
        elif name == 'freqtrade.persistence' and name not in self.patched_modules:
            self.patch_persistence(module)
            self.patched_modules.add(name)

        return module

    def patch_freqtradebot(self, module):
        try:
            from sqlite3 import OperationalError as _SQLOpErr
            from sqlalchemy.exc import OperationalError as _SAOpErr

            if hasattr(module, 'FreqtradeBot'):
                # Patch startup_backpopulate_precision
                if hasattr(module.FreqtradeBot, 'startup_backpopulate_precision'):
                    _orig_method = module.FreqtradeBot.startup_backpopulate_precision

                    def safe_startup_backpopulate_precision(self):
                        try:
                            print('ULTIMATE PATCH: startup_backpopulate_precision: Starting with error handling')
                            return _orig_method(self)
                        except (_SQLOpErr, _SAOpErr) as e:
                            print(f'ULTIMATE PATCH: startup_backpopulate_precision: Database error (ignoring): {e}')
                            return None
                        except Exception as e:
                            print(f'ULTIMATE PATCH: startup_backpopulate_precision: Unexpected error (ignoring): {e}')
                            return None

                    module.FreqtradeBot.startup_backpopulate_precision = safe_startup_backpopulate_precision
                    print('ULTIMATE PATCH: Patched FreqtradeBot.startup_backpopulate_precision via import hook')

        except Exception as e:
            print(f'ULTIMATE PATCH: Failed to patch freqtradebot: {e}')

    def patch_trade_model(self, module):
        try:
            from sqlite3 import OperationalError as _SQLOpErr
            from sqlalchemy.exc import OperationalError as _SAOpErr

            # Create a mock query object that behaves like SQLAlchemy query
            class MockQuery:
                def __init__(self, data=None):
                    self.data = data or []

                def all(self):
                    return self.data

                def first(self):
                    return self.data[0] if self.data else None

                def count(self):
                    return len(self.data)

                def filter(self, *args, **kwargs):
                    return MockQuery(self.data)

                def order_by(self, *args, **kwargs):
                    return MockQuery(self.data)

                def limit(self, *args, **kwargs):
                    return MockQuery(self.data)

            if hasattr(module, 'Trade') and hasattr(module.Trade, 'get_trades'):
                _orig_get_trades = module.Trade.get_trades

                @classmethod
                def safe_get_trades(cls, trade_filter=None, include_orders: bool = True):
                    try:
                        print('ULTIMATE PATCH: Trade.get_trades: Starting with error handling')
                        return _orig_get_trades(trade_filter, include_orders)
                    except (_SQLOpErr, _SAOpErr) as e:
                        print(f'ULTIMATE PATCH: Trade.get_trades: Database error (returning MockQuery): {e}')
                        return MockQuery([])
                    except Exception as e:
                        print(f'ULTIMATE PATCH: Trade.get_trades: Unexpected error (returning MockQuery): {e}')
                        return MockQuery([])

                module.Trade.get_trades = safe_get_trades
                print('ULTIMATE PATCH: Patched Trade.get_trades via import hook')

        except Exception as e:
            print(f'ULTIMATE PATCH: Failed to patch trade_model: {e}')

    def patch_persistence(self, module):
        try:
            from sqlite3 import OperationalError as _SQLOpErr
            from sqlalchemy.exc import OperationalError as _SAOpErr

            # Create a mock query object that behaves like SQLAlchemy query
            class MockQuery:
                def __init__(self, data=None):
                    self.data = data or []

                def all(self):
                    return self.data

                def first(self):
                    return self.data[0] if self.data else None

                def count(self):
                    return len(self.data)

                def filter(self, *args, **kwargs):
                    return MockQuery(self.data)

                def order_by(self, *args, **kwargs):
                    return MockQuery(self.data)

                def limit(self, *args, **kwargs):
                    return MockQuery(self.data)

            if hasattr(module, 'Trade') and hasattr(module.Trade, 'get_trades'):
                _orig_get_trades = module.Trade.get_trades

                @classmethod
                def safe_get_trades_persistence(cls, trade_filter=None, include_orders: bool = True):
                    try:
                        print('ULTIMATE PATCH: persistence.Trade.get_trades: Starting with error handling')
                        return _orig_get_trades(trade_filter, include_orders)
                    except (_SQLOpErr, _SAOpErr) as e:
                        print(f'ULTIMATE PATCH: persistence.Trade.get_trades: Database error (returning MockQuery): {e}')
                        return MockQuery([])
                    except Exception as e:
                        print(f'ULTIMATE PATCH: persistence.Trade.get_trades: Unexpected error (returning MockQuery): {e}')
                        return MockQuery([])

                module.Trade.get_trades = safe_get_trades_persistence
                print('ULTIMATE PATCH: Patched persistence.Trade.get_trades via import hook')

        except Exception as e:
            print(f'ULTIMATE PATCH: Failed to patch persistence: {e}')

# Install the ultimate patch system
try:
    patcher = ModulePatcher()
    __builtins__.__import__ = patcher.patch_import
    print('ULTIMATE PATCH SYSTEM: Installed comprehensive import hook for FreqTrade modules')
except Exception as e:
    print(f'ULTIMATE PATCH SYSTEM: Failed to install import hook: {e}')
