# Clean sitecustomize.py that only includes essential libsql support
# WITHOUT the problematic wallet patches that break dry run balance

import os, importlib, sys
from sqlalchemy.pool import NullPool
from sqlalchemy import create_engine as _orig_create_engine
import sqlalchemy

# adjust Python path to include Freqtrade code
sys.path.insert(0, '/freqtrade')
sys.path.insert(1, '/freqtrade/freqtrade')

def create_engine(*args, **kwargs):
    """Patch create_engine to use NullPool for libsql connections"""
    url = args[0] if args else kwargs.get('url', '')
    if isinstance(url, str) and url.startswith(('sqlite+libsql://', 'libsql://')):
        kwargs.setdefault('poolclass', NullPool)
    return _orig_create_engine(*args, **kwargs)

# Apply the essential libsql patch
sqlalchemy.create_engine = create_engine

print("✅ CLEAN SITECUSTOMIZE: Applied essential libsql support without wallet patches")

# Initialize database tables if needed
def init_database_tables():
    """Initialize database tables using Freqtrade's native system"""
    db_url = os.environ.get('DB_URL', '')
    if not db_url:
        return
    
    try:
        from freqtrade.persistence import init_db
        print(f"🔧 CLEAN SITECUSTOMIZE: Initializing database tables...")
        init_db(db_url, clean_open_orders=False)
        print("✅ CLEAN SITECUSTOMIZE: Database tables initialized")
    except Exception as e:
        print(f"⚠️ CLEAN SITECUSTOMIZE: Database initialization failed: {e}")

# Initialize tables on import
init_database_tables()

print("✅ CLEAN SITECUSTOMIZE: Setup complete - wallet functionality preserved")
