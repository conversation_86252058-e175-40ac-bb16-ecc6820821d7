{"userId": "Js1Gaz4sMPPiDNgFbmAgDFLe4je2", "max_open_trades": 25, "stake_currency": "USD", "stake_amount": 100, "tradable_balance_ratio": 1, "dry_run": true, "dry_run_wallet": {"USD": 1000000, "BTC": 1, "ETH": 10}, "cancel_open_orders_on_exit": false, "trading_mode": "spot", "margin_mode": "isolated", "strategy": "EmaRsiStrategy", "db_url": "sqlite+libsql:///?url=libsql%3A%2F%2F%3AeyJhbGciOiJFZERTQSIsInR5cCI6IkpXVCJ9.eyJqdGkiOiIzR09JU0RIMkVmQ2FaUjdmQlczOTFRIn0.HHLaGF6-ZLhbcdRiTFPWN6OXDKIreIlAzgpC-agv0hAYmHmJgXiiZPOKm-3qTj3zwLEO4aQULgp965PtVfrSAg%40bot-js1gaz4smppidngfbmagdfle4je2-anshjarvis2003-bot2-admin0.aws-us-east-1.turso.io%3A443%2Fbot-js1gaz4smppidngfbmagdfle4je2-anshjarvis2003-bot2", "logfile": "/freqtrade/user_data/logs/freqtrade.log", "timeframe": "15m", "unfilledtimeout": {"entry": 10, "exit": 10, "exit_timeout_count": 0, "unit": "minutes"}, "entry_pricing": {"price_side": "same", "use_order_book": true, "order_book_top": 1, "price_last_balance": 0, "check_depth_of_market": {"enabled": false, "bids_to_ask_delta": 1}}, "exit_pricing": {"price_side": "same", "use_order_book": true, "order_book_top": 1}, "exchange": {"name": "kraken", "key": "", "secret": "", "ccxt_config": {}, "ccxt_async_config": {}, "pair_whitelist": ["BTC/USD", "ETH/USD", "ADA/USD", "SOL/USD", "DOT/USD"], "pair_blacklist": []}, "pairlists": [{"method": "StaticPairList"}], "telegram": {"enabled": false, "token": "", "chat_id": ""}, "api_server": {"enabled": true, "listen_ip_address": "0.0.0.0", "listen_port": 8194, "verbosity": "info", "enable_openapi": true, "jwt_secret_key": "aVeryStr0ngStaticPrefix!_anshjarvis2003-bot1_KeepSecret", "CORS_origins": ["*"], "username": "admin", "password": "password"}, "bot_name": "Js1Gaz4sMPPiDNgFbmAgDFLe4je2-bot", "initial_state": "running", "force_entry_enable": false, "internals": {"process_throttle_secs": 30}}