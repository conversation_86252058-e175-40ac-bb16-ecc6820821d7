#!/bin/bash

# Script to verify that all bot instances are using the correct patched image
# Usage: ./verify-bot-image.sh

echo "=== Freqtrade Bot Image Verification ==="
echo "Expected image: freqtrade-patched:latest"
echo ""

# Check running containers
echo "1. Checking currently running bot containers:"
docker ps --format "table {{.Names}}\t{{.Image}}\t{{.Status}}" | grep freqtrade

echo ""
echo "2. Checking bot manager service environment:"
systemctl show bot-manager.service --property=Environment | grep FREQTRADE_IMAGE

echo ""
echo "3. Checking bot manager process environment:"
ps aux | grep "node.*index.js" | grep -v grep | awk '{print $2}' | head -1 | xargs -I {} cat /proc/{}/environ 2>/dev/null | tr '\0' '\n' | grep FREQTRADE_IMAGE

echo ""
echo "4. Checking docker-compose.yml files in bot instances:"
find /root/freqtrade-instances -name "docker-compose.yml" -exec echo "File: {}" \; -exec grep "image:" {} \; -exec echo "" \;

echo ""
echo "5. Summary:"
WRONG_IMAGE_COUNT=$(docker ps --format "{{.Names}}\t{{.Image}}" | grep freqtrade | grep -v "freqtrade-patched:latest" | wc -l)
TOTAL_BOT_COUNT=$(docker ps --format "{{.Names}}" | grep freqtrade | wc -l)

if [ "$WRONG_IMAGE_COUNT" -eq 0 ]; then
    echo "✅ SUCCESS: All $TOTAL_BOT_COUNT running bot(s) are using the correct patched image!"
else
    echo "❌ WARNING: $WRONG_IMAGE_COUNT out of $TOTAL_BOT_COUNT bot(s) are using the wrong image!"
    echo "   Run the following to fix any bots with wrong images:"
    echo "   1. Update their docker-compose.yml files to use 'freqtrade-patched:latest'"
    echo "   2. Restart the containers with 'docker-compose down && docker-compose up -d'"
fi

echo ""
echo "=== Verification Complete ==="
