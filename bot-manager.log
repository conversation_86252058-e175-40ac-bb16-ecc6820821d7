nohup: ignoring input
Using TURSO_CMD: /root/.turso/turso
Firebase Admin SDK initialized with service account
Firebase was initialized by the main application. auth.js will use the existing instance.
Using FREQTRADE_IMAGE: freqtradeorg/freqtrade:stable
CRITICAL: All new bots will use the patched image: freqtradeorg/freqtrade:stable
Ensuring bot instance base directory exists: /root/freqtrade-instances
=================================================
 Freqtrade Bot Manager (Shared Data Provision)
-------------------------------------------------
 Service Listening on: http://0.0.0.0:3001
 Bot Instance Base Dir: /root/freqtrade-instances
 Main Strategies Source: /root/freqtrade/user_data/strategies
 SHARED Data Directory: /root/freqtrade_shared_data
 Host Freqtrade Needed: NO (for provisioning) / YES (for managing shared data)
=================================================
Ensuring shared strategies directory exists (for fallback): /root/bot-manager/freqtrade-shared/strategies
Checking main strategies source directory: /root/freqtrade/user_data/strategies
Main strategies source directory found.
Ensuring base shared data directory exists: /root/freqtrade_shared_data
Base directories ensured/checked.
