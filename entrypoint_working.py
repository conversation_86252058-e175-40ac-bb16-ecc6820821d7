#!/usr/bin/env python3

import os
import sys
import subprocess
import time

def main():
    """Simple working entrypoint that starts Freqtrade without problematic patches"""
    
    print("🚀 STARTING FREQTRADE WITH WORKING CONFIGURATION")
    print("✅ Database tables pre-created in Turso")
    print("✅ Configuration verified with 1M USD dry run wallet")
    print("✅ Bypassing problematic patches")
    
    # Start Freqtrade directly
    freqtrade_cmd = [
        'freqtrade', 'trade',
        '--config', '/freqtrade/config.json',
        '--strategy-path', '/freqtrade/user_data/strategies'
    ]
    
    print(f"🎯 Executing: {' '.join(freqtrade_cmd)}")
    
    try:
        # Use exec to replace the current process
        os.execvp('freqtrade', freqtrade_cmd)
    except Exception as e:
        print(f"❌ Failed to start Freqtrade: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
